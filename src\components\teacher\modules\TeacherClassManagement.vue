<template>
  <div class="student-management-panel">
    <!-- 操作按钮区域 -->
    <div class="class-actions">
      <div class="actions-right">
        <button class="btn btn-outline" @click="createNewClass">
          <i class="btn-icon plus-icon"></i>
          新建班级
        </button>
        <button class="btn btn-primary" @click="manageStudents">
          <i class="btn-icon management-icon"></i>
          学生管理
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="classesLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p class="loading-text">正在加载班级列表...</p>
    </div>

    <!-- 班级卡片网格 -->
    <div v-else-if="classes.length > 0" class="classes-grid">
      <div
        v-for="classItem in classes"
        :key="classItem.id"
        :class="['class-card', `class-card-${classItem.type}`]"
      >
        <div class="class-header">
          <div class="class-info">
            <h3 class="class-title">{{ classItem.name || '未命名班级' }}</h3>
            <div class="class-semester">{{ classItem.semester || '学期信息未设置' }}</div>
            <div v-if="classItem.teacher" class="class-teacher">教师: {{ classItem.teacher }}</div>
          </div>
          <div class="class-card-actions">
            <button
              class="btn-icon edit-btn"
              @click.stop="editClass(classItem)"
              title="编辑班级"
            >
              编辑
            </button>
            <button
              class="btn-icon delete-btn"
              @click.stop="deleteClass(classItem)"
              title="删除班级"
            >
              删除
            </button>
          </div>
        </div>
        <div class="class-stats" @click="enterClass(classItem.id)">
          <div class="stat-item">
            <span class="stat-label">学生人数</span>
            <span class="stat-value">{{ classItem.studentCount || 0 }}人</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">课程</span>
            <span class="stat-value">{{ classItem.courseCount || 0 }}门</span>
          </div>
        </div>
        <div class="class-meta" @click="enterClass(classItem.id)">
          <div class="meta-item" v-if="classItem.createTime">
            <span class="meta-label">创建时间:</span>
            <span class="meta-value">{{ formatDate(classItem.createTime) }}</span>
          </div>
          <div class="meta-item" v-if="classItem.remark">
            <span class="meta-label">备注:</span>
            <span class="meta-value">{{ classItem.remark }}</span>
          </div>
        </div>
        <div class="progress-section" @click="enterClass(classItem.id)" v-if="classItem.avgProgress !== undefined">
          <div class="progress-label">平均进度: {{ classItem.avgProgress || 0 }}%</div>
          <div class="progress-bar">
            <div class="progress-fill" :style="`width: ${classItem.avgProgress || 0}%`"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">🏫</div>
      <h3 class="empty-title">还没有创建任何班级</h3>
      <p class="empty-description">点击上方的"新建班级"按钮开始创建您的第一个班级</p>
      <button class="btn btn-primary" @click="createNewClass">
        <i class="btn-icon plus-icon"></i>
        创建第一个班级
      </button>
    </div>

    <!-- 创建班级对话框 -->
    <div v-if="showCreateClassDialog" class="dialog-overlay" @click="cancelCreateClass">
      <div class="dialog-content" @click.stop>
        <div class="dialog-header">
          <h3 class="dialog-title">创建新班级</h3>
          <button class="dialog-close" @click="cancelCreateClass">×</button>
        </div>

        <div class="dialog-body">
          <div class="form-group">
            <label class="form-label">班级名称 *</label>
            <input
              v-model="newClassForm.name"
              type="text"
              class="form-input"
              placeholder="请输入班级名称"
              :disabled="createClassLoading"
            />
          </div>
        </div>

        <div class="dialog-footer">
          <button
            class="btn btn-secondary"
            @click="cancelCreateClass"
            :disabled="createClassLoading"
          >
            取消
          </button>
          <button
            class="btn btn-primary"
            @click="confirmCreateClass"
            :disabled="createClassLoading"
          >
            <span v-if="createClassLoading">创建中...</span>
            <span v-else>确认创建</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑班级对话框 -->
    <div v-if="showEditClassDialog" class="dialog-overlay" @click="cancelEditClass">
      <div class="dialog-content" @click.stop>
        <div class="dialog-header">
          <h3 class="dialog-title">编辑班级</h3>
          <button class="dialog-close" @click="cancelEditClass">×</button>
        </div>

        <div class="dialog-body">
          <!-- 显示班级ID用于调试 -->
          <div class="form-group">
            <label class="form-label">班级ID</label>
            <input
              v-model="editClassForm.id"
              type="number"
              class="form-input"
              placeholder="班级ID"
              :disabled="true"
              readonly
            />
          </div>

          <div class="form-group">
            <label class="form-label">班级名称 *</label>
            <input
              v-model="editClassForm.name"
              type="text"
              class="form-input"
              placeholder="请输入班级名称"
              :disabled="editClassLoading"
            />
          </div>
        </div>

        <div class="dialog-footer">
          <button
            class="btn btn-secondary"
            @click="cancelEditClass"
            :disabled="editClassLoading"
          >
            取消
          </button>
          <button
            class="btn btn-primary"
            @click="confirmEditClass"
            :disabled="editClassLoading"
          >
            <span v-if="editClassLoading">更新中...</span>
            <span v-else>确认更新</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { getCurrentUser } from '@/api/auth';
import { getClassesList, createClass, updateClass, deleteClasses, getClassById } from '@/api/class';

const router = useRouter();

// 当前用户信息
const currentUser = ref(getCurrentUser() || {});

// 班级数据
const classes = ref([]);
const classesLoading = ref(false);

// 新建班级对话框状态
const showCreateClassDialog = ref(false);
const newClassForm = ref({
  name: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: '',
  remark: '',
  params: {},
  id: 0
});
const createClassLoading = ref(false);

// 编辑班级对话框状态
const showEditClassDialog = ref(false);
const editClassForm = ref({
  name: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: '',
  remark: '',
  params: {},
  id: 0
});
const editClassLoading = ref(false);

// 组件挂载时加载数据
onMounted(async () => {
  await fetchClasses();
});

// 获取班级列表
const fetchClasses = async () => {
  classesLoading.value = true;
  try {
    console.log('开始获取班级列表...');
    const response = await getClassesList();
    console.log('班级列表API响应:', response);

    if (response && response.rows && Array.isArray(response.rows)) {
      console.log('班级原始数据:', response.rows);

      // 将API返回的班级数据映射到组件需要的格式
      classes.value = response.rows.map((classItem, index) => {
        console.log(`处理班级数据 ${index + 1}:`, classItem);

        const mappedClass = {
          id: classItem.id,
          name: classItem.name || '未命名班级',
          semester: classItem.semester || '2024春季学期',
          studentCount: classItem.studentCount !== undefined ? classItem.studentCount : 0,
          courseCount: classItem.courseCount !== undefined ? classItem.courseCount : 0,
          avgProgress: classItem.avgProgress !== undefined ? classItem.avgProgress : 0,
          type: index % 2 === 0 ? 'blue' : 'green',
          teacher: classItem.teacher || classItem.createBy || currentUser.value.username || '未知教师',
          startDate: classItem.startDate || '2024-02-20',
          endDate: classItem.endDate || '2024-06-30',
          createBy: classItem.createBy,
          createTime: classItem.createTime,
          updateBy: classItem.updateBy,
          updateTime: classItem.updateTime,
          remark: classItem.remark || ''
        };

        console.log(`映射后的班级数据 ${index + 1}:`, mappedClass);
        return mappedClass;
      });

      console.log('班级列表加载成功，总数:', classes.value.length);
      console.log('最终班级数据:', classes.value);
    } else {
      console.warn('班级列表数据格式不正确:', response);
      classes.value = [];
    }
  } catch (error) {
    console.error('获取班级列表失败:', error);
    classes.value = [];
  } finally {
    classesLoading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知';
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '日期格式错误';
  }
};

// 班级管理相关方法
const createNewClass = async () => {
  console.log('显示创建新班级对话框');
  // 重置表单
  newClassForm.value = {
    name: '',
    createBy: currentUser.value.username || '',
    createTime: '',
    updateBy: currentUser.value.username || '',
    updateTime: '',
    remark: '',
    params: {},
    id: 0
  };
  showCreateClassDialog.value = true;
};

// 确认创建班级
const confirmCreateClass = async () => {
  if (!newClassForm.value.name.trim()) {
    alert('请输入班级名称');
    return;
  }

  createClassLoading.value = true;
  try {
    const classData = {
      ...newClassForm.value,
      createBy: currentUser.value.username || '',
      updateBy: currentUser.value.username || '',
      params: {}
    };

    console.log('正在创建班级:', classData);
    const response = await createClass(classData);

    if (response && response.success) {
      console.log('班级创建成功');
      alert('班级创建成功！');
      showCreateClassDialog.value = false;
      // 重新获取班级列表
      await fetchClasses();
    } else {
      console.error('班级创建失败:', response);
      alert('班级创建失败，请重试');
    }
  } catch (error) {
    console.error('创建班级时发生错误:', error);
    alert('创建班级时发生错误，请重试');
  } finally {
    createClassLoading.value = false;
  }
};

// 取消创建班级
const cancelCreateClass = () => {
  showCreateClassDialog.value = false;
};

// 编辑班级
const editClass = (classItem) => {
  console.log('编辑班级:', classItem);
  // 填充表单数据，确保所有必要字段都有值
  editClassForm.value = {
    id: classItem.id || 0,
    name: classItem.name || '',
    createBy: classItem.createBy || '',
    createTime: classItem.createTime || '',
    updateBy: currentUser.value.username || '',
    updateTime: new Date().toISOString(),
    remark: classItem.remark || '',
    params: classItem.params || {}
  };
  console.log('编辑表单数据:', editClassForm.value);
  showEditClassDialog.value = true;
};

// 确认编辑班级
const confirmEditClass = async () => {
  // 验证必填字段
  if (!editClassForm.value.name.trim()) {
    alert('请输入班级名称');
    return;
  }

  if (!editClassForm.value.id || editClassForm.value.id === 0) {
    alert('班级ID不能为空');
    return;
  }

  editClassLoading.value = true;
  try {
    // 构建符合API要求的数据格式
    const classData = {
      id: editClassForm.value.id,
      name: editClassForm.value.name.trim(),
      createBy: editClassForm.value.createBy || '',
      createTime: editClassForm.value.createTime || '',
      updateBy: currentUser.value.username || '',
      updateTime: new Date().toISOString(),
      remark: editClassForm.value.remark || '',
      params: editClassForm.value.params || {}
    };

    console.log('正在更新班级:', classData);
    const response = await updateClass(classData);

    if (response && response.success) {
      console.log('班级更新成功');
      alert('班级更新成功！');
      showEditClassDialog.value = false;
      // 重新获取班级列表
      await fetchClasses();
    } else {
      console.error('班级更新失败:', response);
      alert(response.msg || '班级更新失败，请重试');
    }
  } catch (error) {
    console.error('更新班级时发生错误:', error);
    alert('更新班级时发生错误，请重试');
  } finally {
    editClassLoading.value = false;
  }
};

// 取消编辑班级
const cancelEditClass = () => {
  showEditClassDialog.value = false;
};

// 删除班级
const deleteClass = async (classItem) => {
  console.log('删除班级:', classItem);

  // 确认删除
  const confirmDelete = confirm(`确定要删除班级"${classItem.name}"吗？此操作不可撤销。`);
  if (!confirmDelete) {
    return;
  }

  try {
    console.log('正在删除班级...', classItem.id);
    const response = await deleteClasses([classItem.id]);

    if (response && response.success) {
      console.log('班级删除成功');
      alert('班级删除成功！');
      // 重新获取班级列表
      await fetchClasses();
    } else {
      console.error('班级删除失败:', response);
      alert(response.msg || '班级删除失败，请重试');
    }
  } catch (error) {
    console.error('删除班级时发生错误:', error);
    alert('删除班级时发生错误，请重试');
  }
};

const manageStudents = () => {
  console.log('学生管理');
  alert('学生管理功能正在开发中，可以在这里查看和管理所有学生信息');
};

const enterClass = (classId) => {
  console.log('进入班级:', classId);
  const selectedClass = classes.value.find(c => c.id === classId);
  if (selectedClass) {
    // 使用路由跳转到班级详情页面
    router.push({
      name: 'teacher-class-detail',
      params: { id: classId }
    });
  } else {
    console.error('未找到指定的班级:', classId);
    alert('未找到指定的班级');
  }
};

// 暴露方法给父组件
defineExpose({
  fetchClasses
});
</script>

<style scoped>
/* 班级管理面板样式 */
.student-management-panel {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 班级管理样式 */
.class-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
}

.class-actions .actions-right {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* 班级卡片网格 */
.classes-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

/* 加载状态样式 */
.loading-state {
  text-align: center;
  padding: 4rem 2rem;
  margin-top: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-color-secondary);
  font-size: 1rem;
  margin: 0;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  margin-top: 1rem;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.6;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1rem 0;
}

.empty-description {
  font-size: 1rem;
  color: var(--text-color-secondary);
  margin: 0 0 2rem 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* 班级卡片样式 */
.class-card {
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: #fff;
  border: 1px solid #e5e7eb;
  aspect-ratio: 16 / 9;
  cursor: pointer;
}

.class-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #d1d5db;
}

.class-card-blue {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: #fff;
  border: none;
}

.class-card-green {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: #fff;
  border: none;
}

.class-header {
  padding: 0.75rem;
  position: relative;
  z-index: 1;
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 0.5rem;
}

.class-info {
  flex: 1;
}

.class-card-blue .class-header,
.class-card-green .class-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.class-card-actions {
  display: flex;
  gap: 0.25rem;
  margin-left: 1rem;
  align-items: center;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.edit-btn,
.delete-btn {
  background: transparent;
  border: 1px solid transparent;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.75rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  font-weight: 500;
  line-height: 1;
  min-width: 44px;
  min-height: 32px;
  position: relative;
  z-index: 3;
}

.edit-btn {
  color: #666;
}

.edit-btn:hover {
  background: rgba(0, 0, 0, 0.08);
  border-color: rgba(0, 0, 0, 0.1);
}

.delete-btn {
  color: #dc2626;
}

.delete-btn:hover {
  background: rgba(220, 38, 38, 0.08);
  border-color: rgba(220, 38, 38, 0.2);
  color: #b91c1c;
}

.class-card-blue .edit-btn,
.class-card-green .edit-btn,
.class-card-blue .delete-btn,
.class-card-green .delete-btn {
  color: #fff;
}

.class-card-blue .edit-btn:hover,
.class-card-green .edit-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}

.class-card-blue .delete-btn:hover,
.class-card-green .delete-btn:hover {
  background: rgba(220, 38, 38, 0.2);
  border-color: rgba(220, 38, 38, 0.3);
  color: #fff;
}

.class-title {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 700;
  color: inherit;
  line-height: 1.2;
}

.class-semester {
  font-size: 0.75rem;
  opacity: 0.9;
  margin-bottom: 0.25rem;
}

.class-teacher {
  font-size: 0.7rem;
  opacity: 0.8;
  font-style: italic;
}

.class-stats {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.class-card-blue .class-stats,
.class-card-green .class-stats {
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
}

.class-meta {
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  font-size: 0.75rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.class-card-blue .class-meta,
.class-card-green .class-meta {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.meta-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  font-weight: 500;
  opacity: 0.8;
}

.meta-value {
  opacity: 0.9;
  text-align: right;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.class-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.class-stats .stat-label {
  font-size: 0.65rem;
  opacity: 0.8;
}

.class-stats .stat-value {
  font-size: 0.875rem;
  font-weight: 600;
}

.class-card-blue .class-stats .stat-value,
.class-card-green .class-stats .stat-value {
  color: #fff;
}

.class-card .progress-section {
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  flex: 1;
}

.class-card-blue .progress-section,
.class-card-green .progress-section {
  background: rgba(255, 255, 255, 0.15);
}

.class-card .progress-label {
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.class-card-blue .progress-label,
.class-card-green .progress-label {
  color: #fff;
  opacity: 0.9;
}

.class-card .progress-bar {
  height: 4px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.class-card-blue .progress-bar,
.class-card-green .progress-bar {
  background-color: rgba(255, 255, 255, 0.2);
}

.class-card .progress-fill {
  height: 100%;
  background-color: #4299e1;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.class-card-blue .progress-fill {
  background-color: #fff;
}

.class-card-green .progress-fill {
  background-color: #fff;
}

/* 按钮样式 */
.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  white-space: nowrap;
  text-transform: none;
  letter-spacing: 0.025em;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: #fff;
  box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(66, 153, 225, 0.4);
}

.btn-secondary {
  background-color: #f7fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background-color: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  border: 2px solid rgba(66, 153, 225, 0.5);
  color: #4299e1;
}

.btn-outline:hover {
  background: rgba(66, 153, 225, 0.1);
  border-color: #4299e1;
  transform: translateY(-1px);
}

.btn-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  vertical-align: middle;
}

.plus-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z'/%3E%3C/svg%3E");
}

.btn-primary .plus-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23fff'%3E%3Cpath d='M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z'/%3E%3C/svg%3E");
}

.management-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23fff'%3E%3Cpath d='M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.998 1.998 0 0 0 18 7h-2c-.8 0-1.54.37-2.01 1.01l-.74 2.22-2.99-2.99C10.05 6.85 9.49 6.67 8.9 6.67c-1.66 0-3 1.34-3 3 0 .59.18 1.15.5 1.61L9 14.89V22h2v-6h2v6h4z'/%3E%3C/svg%3E");
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.dialog-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.dialog-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-close:hover {
  color: #374151;
}

.dialog-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.dialog-footer {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.dialog-footer .btn {
  min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .classes-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 900px) {
  .classes-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .class-actions {
    justify-content: flex-start;
  }

  .class-actions .actions-right {
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 640px) {
  .dialog-content {
    width: 95%;
    margin: 1rem;
  }

  .dialog-header,
  .dialog-body,
  .dialog-footer {
    padding: 1rem;
  }

  .dialog-footer {
    flex-direction: column;
  }

  .dialog-footer .btn {
    width: 100%;
  }
}

@media (max-width: 600px) {
  .classes-grid {
    grid-template-columns: 1fr;
  }
}
</style>
